<!DOCTYPE html>
<html lang="en" data-theme="{{ current_theme }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}PEPE Store - Top #1 Tools for PC{% endblock %}</title>
    <meta name="description" content="Discover powerful tools and premium software for your PC at PEPE Store. Fast downloads, top-rated apps, and more." />
    <meta name="keywords" content="PEPE Store, PC tools, software for PC, premium apps, download software, top PC applications, PC utilities" />
    <meta name="author" content="PEPE Store Team" />
    <meta property="og:title" content="PEPE Store - Top #1 Tools for PC" />
    <meta property="og:description" content="Join the PEPE community and access top-rated tools for Windows. Explore powerful PC software trusted by over 1M users." />
    <meta property="og:image" content="https://pepestore.pythonanywhere.com/static/assets/pepe.jpeg" />
    <meta property="og:url" content="https://pepestore.pythonanywhere.com/" />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="PEPE Store - Top #1 Tools for PC" />
    <meta name="twitter:description" content="Discover and download the best tools for your PC. Fast, reliable, and trusted by thousands." />
    <meta name="twitter:image" content="https://pepestore.pythonanywhere.com/static/assets/pepe.jpeg" />
    <link rel="icon" href="/static/favicon.ico" type="image/x-icon" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <script async src="https://www.googletagmanager.com/gtag/js?id=G-V1E02S28YG"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
    
      gtag('config', 'G-V1E02S28YG');
    </script>

    {% block extra_head %}{% endblock %}
</head>
<body>
    <nav class="pepe-navbar">
        <div class="pepe-navbar-container">
            <!-- Brand -->
            <a class="pepe-navbar-brand" href="{{ url_for('main.index') }}">
                <span class="pepe-logo ">🐸</span>
                <span class="pepe-brand-text">PEPE Store</span>
                <span class="pepe-tagline">Top #1 Tools for PC</span>
            </a>

            <!-- Mobile Toggle -->
            <button class="pepe-navbar-toggle" type="button" onclick="toggleMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <!-- Navigation -->
            <div class="pepe-navbar-nav" id="pepeNavbar">
                <div class="pepe-nav-left">
                    <a class="pepe-nav-link" href="{{ url_for('main.index') }}">Home</a>
                    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
                </div>

                <div class="pepe-nav-right">
                    {% if session.user_id %}
                    <!-- User Dropdown -->
                    <div class="pepe-dropdown" id="userDropdown">
                        <button class="pepe-dropdown-toggle" onclick="toggleUserDropdown(event)">
                            <i class="bi bi-person-circle"></i>
                            <span>{{ session.username }}</span>
                            <i class="bi bi-chevron-down pepe-dropdown-arrow"></i>
                        </button>

                        <div class="pepe-dropdown-menu" id="userDropdownMenu">
                            {% if session.role == 'admin' %}
                            <a class="pepe-dropdown-item" href="{{ url_for('admin.dashboard') }}">
                                <i class="bi bi-speedometer2"></i>
                                <span>Admin Dashboard</span>
                            </a>
                            <a class="pepe-dropdown-item" href="{{ url_for('admin.apps') }}">
                                <i class="bi bi-list"></i>
                                <span>Manage Apps</span>
                            </a>
                            <a class="pepe-dropdown-item" href="{{ url_for('admin.users') }}">
                                <i class="bi bi-people"></i>
                                <span>Manage Users</span>
                            </a>
                            <a class="pepe-dropdown-item" href="{{ url_for('admin.blocked_ips') }}">
                                <i class="bi bi-shield-x"></i>
                                <span>Blocked IPs</span>
                            </a>
                            {% else %}
                            <a class="pepe-dropdown-item" href="{{ url_for('publisher.dashboard') }}">
                                <i class="bi bi-house"></i>
                                <span>My Dashboard</span>
                            </a>
                            <a class="pepe-dropdown-item" href="{{ url_for('publisher.reports') }}">
                                <i class="bi bi-flag"></i>
                                <span>Reports & Suggestions</span>
                            </a>
                            {% endif %}
                            <div class="pepe-dropdown-divider"></div>
                            <a class="pepe-dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right"></i>
                                <span>Logout</span>
                            </a>
                        </div>
                    </div>
                    {% else %}
                    <!-- Login Link -->
                    <a class="pepe-nav-link pepe-login-btn" href="{{ url_for('auth.login') }}">
                        <i class="bi bi-box-arrow-in-right"></i>
                        <span>Login</span>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert">x</button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Glassmorphism Footer -->
    <footer class="glass-panel py-4 mt-5" style="border-radius: 0; border-left: none; border-right: none; border-bottom: none;">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="pepe-glow">🐸 PEPE Store</h5>
                    <p class="mb-2 opacity-75">Top #1 destination for PC tools and applications</p>
                    <p class="mb-0">
                        💬 Contact us on
                        <a href="https://t.me/pepestoreapps" class="text-decoration-none" style="color: var(--pepe-primary);" target="_blank">
                            Telegram
                        </a>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0 opacity-75">&copy; 2025 PEPE Store. Powered by the community.</p>
                </div>
            </div>
        </div>
    </footer>

    {% block extra_scripts %}{% endblock %}
</body>
</html>
