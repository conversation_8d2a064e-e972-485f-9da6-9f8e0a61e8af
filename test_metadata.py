#!/usr/bin/env python3
"""
Test metadata generation
"""

from app import create_app
from app.utils.seo import MetadataGenerator

app = create_app()
with app.app_context():
    # Test default metadata
    default_meta = MetadataGenerator.get_default_metadata()
    print('Default metadata:')
    print(f'  Title: {default_meta["title"]}')
    print(f'  Description: {default_meta["description"][:100]}...')
    print()
    
    # Test category metadata
    category_meta = MetadataGenerator.generate_category_metadata('Software')
    print('Category metadata:')
    print(f'  Title: {category_meta["title"]}')
    print(f'  Description: {category_meta["description"][:100]}...')
    print()
    
    # Test search metadata
    search_meta = MetadataGenerator.generate_search_metadata('test search')
    print('Search metadata:')
    print(f'  Title: {search_meta["title"]}')
    print(f'  Description: {search_meta["description"][:100]}...')
    print()
    
    print('All metadata tests passed!')
