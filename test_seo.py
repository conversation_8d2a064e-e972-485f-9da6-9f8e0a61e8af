#!/usr/bin/env python3
"""
SEO Testing Script
Tests the SEO implementation to ensure search engine bots can access pages
and metadata is properly generated.
"""

import requests
import sys
from urllib.parse import urljoin
from bs4 import BeautifulSoup

# Test configuration
BASE_URL = "http://localhost:5000"  # Change to your server URL
SEARCH_ENGINE_USER_AGENTS = [
    "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)",
    "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)",
    "Mozilla/5.0 (compatible; Yahoo! Slurp; http://help.yahoo.com/help/us/ysearch/slurp)",
    "Mozilla/5.0 (compatible; DuckDuckBot-Https/1.1; https://duckduckgo.com/duckduckbot)"
]

def test_url_accessibility(url, user_agent):
    """Test if a URL is accessible with a given user agent"""
    try:
        headers = {'User-Agent': user_agent}
        response = requests.get(url, headers=headers, timeout=10)
        return response.status_code, response.text
    except Exception as e:
        return None, str(e)

def extract_metadata(html_content):
    """Extract metadata from HTML content"""
    soup = BeautifulSoup(html_content, 'html.parser')
    metadata = {}
    
    # Extract title
    title_tag = soup.find('title')
    metadata['title'] = title_tag.text if title_tag else None
    
    # Extract meta tags
    meta_tags = soup.find_all('meta')
    for tag in meta_tags:
        if tag.get('name'):
            metadata[tag.get('name')] = tag.get('content')
        elif tag.get('property'):
            metadata[tag.get('property')] = tag.get('content')
    
    return metadata

def test_sitemap():
    """Test sitemap accessibility and validity"""
    print("Testing sitemap...")
    
    for user_agent in SEARCH_ENGINE_USER_AGENTS:
        print(f"  Testing with {user_agent.split('/')[1].split(';')[0]}...")
        
        sitemap_url = urljoin(BASE_URL, '/sitemap.xml')
        status_code, content = test_url_accessibility(sitemap_url, user_agent)
        
        if status_code == 200:
            print(f"    ✓ Sitemap accessible (Status: {status_code})")
            if '<?xml version="1.0"' in content and '<urlset' in content:
                print("    ✓ Valid XML sitemap format")
            else:
                print("    ✗ Invalid XML sitemap format")
        else:
            print(f"    ✗ Sitemap not accessible (Status: {status_code})")
            print(f"    Error: {content}")

def test_robots_txt():
    """Test robots.txt accessibility"""
    print("Testing robots.txt...")
    
    for user_agent in SEARCH_ENGINE_USER_AGENTS:
        print(f"  Testing with {user_agent.split('/')[1].split(';')[0]}...")
        
        robots_url = urljoin(BASE_URL, '/robots.txt')
        status_code, content = test_url_accessibility(robots_url, user_agent)
        
        if status_code == 200:
            print(f"    ✓ Robots.txt accessible (Status: {status_code})")
            if 'User-agent:' in content and 'Sitemap:' in content:
                print("    ✓ Valid robots.txt format")
            else:
                print("    ✗ Invalid robots.txt format")
        else:
            print(f"    ✗ Robots.txt not accessible (Status: {status_code})")

def test_page_metadata(url, expected_title_contains=None):
    """Test metadata for a specific page"""
    print(f"Testing metadata for {url}...")
    
    for user_agent in SEARCH_ENGINE_USER_AGENTS:
        bot_name = user_agent.split('/')[1].split(';')[0]
        print(f"  Testing with {bot_name}...")
        
        status_code, content = test_url_accessibility(url, user_agent)
        
        if status_code == 200:
            print(f"    ✓ Page accessible (Status: {status_code})")
            
            metadata = extract_metadata(content)
            
            # Check essential metadata
            if metadata.get('title'):
                print(f"    ✓ Title: {metadata['title']}")
                if expected_title_contains and expected_title_contains in metadata['title']:
                    print(f"    ✓ Title contains expected text: {expected_title_contains}")
            else:
                print("    ✗ No title found")
            
            if metadata.get('description'):
                print(f"    ✓ Description: {metadata['description'][:100]}...")
            else:
                print("    ✗ No description found")
            
            if metadata.get('keywords'):
                print(f"    ✓ Keywords: {metadata['keywords'][:100]}...")
            else:
                print("    ✗ No keywords found")
            
            # Check Open Graph tags
            og_tags = ['og:title', 'og:description', 'og:image', 'og:url', 'og:type']
            og_found = sum(1 for tag in og_tags if metadata.get(tag))
            print(f"    ✓ Open Graph tags: {og_found}/{len(og_tags)} found")
            
            # Check Twitter Card tags
            twitter_tags = ['twitter:card', 'twitter:title', 'twitter:description', 'twitter:image']
            twitter_found = sum(1 for tag in twitter_tags if metadata.get(tag))
            print(f"    ✓ Twitter Card tags: {twitter_found}/{len(twitter_tags)} found")
            
        else:
            print(f"    ✗ Page not accessible (Status: {status_code})")

def main():
    """Run all SEO tests"""
    print("=== SEO Implementation Test ===")
    print(f"Testing against: {BASE_URL}")
    print()
    
    # Test sitemap
    test_sitemap()
    print()
    
    # Test robots.txt
    test_robots_txt()
    print()
    
    # Test homepage metadata
    test_page_metadata(BASE_URL, "PEPE Store")
    print()
    
    # Test category page metadata (if categories exist)
    category_url = urljoin(BASE_URL, '/?category=Software')
    test_page_metadata(category_url, "Software")
    print()
    
    # Test search page metadata
    search_url = urljoin(BASE_URL, '/?search=test')
    test_page_metadata(search_url, "Search Results")
    print()
    
    print("=== Test Complete ===")
    print("Review the results above to ensure:")
    print("1. All search engine bots can access sitemap.xml and robots.txt")
    print("2. Pages are accessible without fingerprinting requirements")
    print("3. Dynamic metadata is properly generated for different page types")
    print("4. Open Graph and Twitter Card tags are present")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        BASE_URL = sys.argv[1]
    main()
