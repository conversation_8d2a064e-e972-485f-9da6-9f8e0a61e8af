#!/usr/bin/env python3
"""
Debug bot detection
"""

from app.utils.seo import BotDetector

# Real Discord and Telegram user agents
test_agents = [
    # Discord user agents (real ones)
    'Mozilla/5.0 (compatible; Discordbot/2.0; +https://discordapp.com)',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 11.6; rv:92.0) Gecko/20100101 Firefox/92.0 (compatible; Discord/1.0.9003)',
    
    # Telegram user agents (real ones)
    'TelegramBot (like TwitterBot)',
    'Mozilla/5.0 (compatible; TelegramBot/1.0; +https://telegram.org/)',
    
    # Other social media
    'facebookexternalhit/1.1 (+http://www.facebook.com/externalhit_uatext.php)',
    'Twitterbot/1.0',
    
    # Regular browsers (should NOT be detected)
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'curl/7.68.0'
]

print("Testing bot detection with debug output:")
print("=" * 60)

for i, agent in enumerate(test_agents, 1):
    print(f"\nTest {i}: {agent}")
    print("-" * 40)
    
    is_bot, bot_name = BotDetector.is_search_engine_bot(agent, '127.0.0.1')
    should_bypass = BotDetector.should_bypass_security(agent, '127.0.0.1')
    
    print(f"Result: is_bot={is_bot}, bot_name={bot_name}, should_bypass={should_bypass}")
    
    if 'discord' in agent.lower() or 'telegram' in agent.lower():
        if not should_bypass:
            print("❌ ERROR: Discord/Telegram bot should bypass security!")
        else:
            print("✅ SUCCESS: Discord/Telegram bot correctly detected")
    elif 'mozilla' in agent.lower() and 'chrome' in agent.lower():
        if should_bypass:
            print("❌ ERROR: Regular browser should NOT bypass security!")
        else:
            print("✅ SUCCESS: Regular browser correctly blocked")

print("\n" + "=" * 60)
print("Debug complete!")
