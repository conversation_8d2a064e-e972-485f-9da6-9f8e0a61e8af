2025-07-09 18:32:47,883 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 18:32:47,883 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 18:32:51,916 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:51] "GET / HTTP/1.1" 200 -
2025-07-09 18:32:52,877 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-09 18:32:52,883 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-09 18:32:52,885 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /uploads/icons/20250613_015832_2.webp HTTP/1.1" 200 -
2025-07-09 18:32:52,887 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /uploads/icons/20250617_202240_2.webp HTTP/1.1" 200 -
2025-07-09 18:32:52,914 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1" 200 -
2025-07-09 18:32:52,933 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1" 200 -
2025-07-09 18:32:52,934 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:52] "GET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1" 200 -
2025-07-09 18:32:53,438 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:53] "GET /uploads/icons/20250612_151120_1.webp HTTP/1.1" 200 -
2025-07-09 18:32:53,449 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:53] "GET /uploads/icons/20250611_190659_venom.webp HTTP/1.1" 200 -
2025-07-09 18:32:53,450 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:53] "GET /uploads/icons/20250611_191431_2.webp HTTP/1.1" 200 -
2025-07-09 18:32:53,462 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:53] "GET /uploads/icons/20250611_221720_1.webp HTTP/1.1" 200 -
2025-07-09 18:32:53,464 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:53] "GET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1" 200 -
2025-07-09 18:32:54,485 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-07-09 18:32:54,802 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "[32mGET /rce HTTP/1.1[0m" 302 -
2025-07-09 18:32:54,816 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 18:32:54,912 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "GET /static/js/client.js HTTP/1.1" 200 -
2025-07-09 18:32:54,934 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:32:54,936 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:32:54,999 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:54] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-09 18:32:55,865 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "GET / HTTP/1.1" 200 -
2025-07-09 18:32:55,884 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,886 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,890 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,893 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,910 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,917 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,932 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,933 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,947 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,948 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,949 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:55,959 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:55] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-09 18:32:59,895 INFO: 127.0.0.1 - - [09/Jul/2025 18:32:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:03,096 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "[32mGET /rce HTTP/1.1[0m" 302 -
2025-07-09 18:33:03,105 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 18:33:03,134 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:03,135 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:03,143 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:03,206 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:03] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-09 18:33:04,157 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "POST /fp/collect HTTP/1.1" 200 -
2025-07-09 18:33:04,697 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "GET / HTTP/1.1" 200 -
2025-07-09 18:33:04,722 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,729 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,733 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,737 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,765 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,772 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,782 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,784 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,789 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,794 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,797 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:04,815 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:04] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-09 18:33:06,293 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:06] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:33:06,317 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:06,319 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:48,648 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:48] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:33:48,674 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:48,675 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:48,856 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:48] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:33:52,902 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 18:33:52,902 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 18:33:52,906 INFO:  * Restarting with stat
2025-07-09 18:33:53,853 WARNING:  * Debugger is active!
2025-07-09 18:33:53,860 INFO:  * Debugger PIN: 437-432-840
2025-07-09 18:33:54,008 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:54] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:33:54,095 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:33:54,097 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:33:54,164 INFO: 127.0.0.1 - - [09/Jul/2025 18:33:54] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:50:47,724 INFO: 127.0.0.1 - - [09/Jul/2025 18:50:47] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:50:48,108 INFO: 127.0.0.1 - - [09/Jul/2025 18:50:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:50:48,127 INFO: 127.0.0.1 - - [09/Jul/2025 18:50:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:50:48,468 INFO: 127.0.0.1 - - [09/Jul/2025 18:50:48] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:51:58,533 INFO: 127.0.0.1 - - [09/Jul/2025 18:51:58] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:51:58,564 INFO: 127.0.0.1 - - [09/Jul/2025 18:51:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:51:58,568 INFO: 127.0.0.1 - - [09/Jul/2025 18:51:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:51:58,682 INFO: 127.0.0.1 - - [09/Jul/2025 18:51:58] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:53:02,160 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:02] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:53:02,204 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:53:02,206 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:53:02,322 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:53:07,294 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:53:09,132 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:09] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:53:09,168 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:53:09,173 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:53:09,514 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:09] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:53:58,841 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:58] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:53:58,883 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:53:58,886 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:53:59,299 INFO: 127.0.0.1 - - [09/Jul/2025 18:53:59] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:54:34,510 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:34] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:54:34,575 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:54:34,579 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:54:34,973 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:34] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:54:38,372 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:38] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:54:38,431 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:38] "GET /static/js/app.js HTTP/1.1" 200 -
2025-07-09 18:54:38,434 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:38] "GET /static/css/style.css HTTP/1.1" 200 -
2025-07-09 18:54:43,607 INFO: 127.0.0.1 - - [09/Jul/2025 18:54:43] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-07-09 18:55:02,104 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:02] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:55:02,150 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:55:02,156 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:55:02,532 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:55:36,113 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:36] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:55:36,174 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:36] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:55:36,177 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:55:36,401 INFO: 127.0.0.1 - - [09/Jul/2025 18:55:36] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 18:56:32,350 INFO: 127.0.0.1 - - [09/Jul/2025 18:56:32] "GET /rce HTTP/1.1" 200 -
2025-07-09 18:56:32,418 INFO: 127.0.0.1 - - [09/Jul/2025 18:56:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 18:56:32,438 INFO: 127.0.0.1 - - [09/Jul/2025 18:56:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 18:56:32,721 INFO: 127.0.0.1 - - [09/Jul/2025 18:56:32] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 19:01:21,895 INFO: 127.0.0.1 - - [09/Jul/2025 19:01:21] "GET /rce HTTP/1.1" 200 -
2025-07-09 19:01:21,946 INFO: 127.0.0.1 - - [09/Jul/2025 19:01:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 19:01:21,951 INFO: 127.0.0.1 - - [09/Jul/2025 19:01:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:01:22,061 INFO: 127.0.0.1 - - [09/Jul/2025 19:01:22] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 19:17:17,470 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\main.py', reloading
2025-07-09 19:17:17,577 INFO:  * Restarting with stat
2025-07-09 19:17:19,212 WARNING:  * Debugger is active!
2025-07-09 19:17:19,228 INFO:  * Debugger PIN: 437-432-840
2025-07-09 19:48:50,610 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 19:48:50,612 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 19:48:57,288 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 89, in index
    return render_template('index.html',
                         apps=apps,
    ...<6 lines>...
                         has_next=has_next,
                         metadata=metadata)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
        endpoint,
    ...<3 lines>...
        force_external=_external,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 103, in index
    return render_template('index.html',
                         apps=type('AppList', (), {'items': [], 'total': 0, 'pages': 1, 'page': 1, 'has_prev': False, 'has_next': False, 'iter_pages': lambda: [1]})(),
    ...<6 lines>...
                         has_next=False,
                         metadata=MetadataGenerator.get_default_metadata())
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
        endpoint,
    ...<3 lines>...
        force_external=_external,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?
2025-07-09 19:48:57,327 INFO: 127.0.0.1 - - [09/Jul/2025 19:48:57] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-07-09 19:48:57,387 INFO: 127.0.0.1 - - [09/Jul/2025 19:48:57] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-09 19:49:08,562 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 89, in index
    return render_template('index.html',
                         apps=apps,
    ...<6 lines>...
                         has_next=has_next,
                         metadata=metadata)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
        endpoint,
    ...<3 lines>...
        force_external=_external,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 103, in index
    return render_template('index.html',
                         apps=type('AppList', (), {'items': [], 'total': 0, 'pages': 1, 'page': 1, 'has_prev': False, 'has_next': False, 'iter_pages': lambda: [1]})(),
    ...<6 lines>...
                         has_next=False,
                         metadata=MetadataGenerator.get_default_metadata())
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
        endpoint,
    ...<3 lines>...
        force_external=_external,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?
2025-07-09 19:49:08,602 INFO: 127.0.0.1 - - [09/Jul/2025 19:49:08] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-07-09 19:55:12,990 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 19:55:12,991 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 19:55:15,757 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 89, in index
    return render_template('index.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 103, in index
    return render_template('index.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?
2025-07-09 19:55:15,774 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:15] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-07-09 19:55:16,515 ERROR: Exception on / [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 89, in index
    return render_template('index.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\blueprints\main.py", line 103, in index
    return render_template('index.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Program Files\Python311\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\index.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 80, in top-level template code
    <!-- <a class="pepe-nav-link" href="{{ url_for('main.rce') }}">RCE</a> -->
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'main.rce'. Did you mean 'main.gate' instead?
2025-07-09 19:55:16,524 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:16] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-07-09 19:55:55,905 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 19:55:55,906 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 19:55:55,911 INFO:  * Restarting with stat
2025-07-09 19:55:57,219 WARNING:  * Debugger is active!
2025-07-09 19:55:57,227 INFO:  * Debugger PIN: 437-432-840
2025-07-09 19:55:57,476 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:57] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-07-09 19:55:57,580 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:57] "GET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-07-09 19:55:57,581 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:57] "GET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-07-09 19:55:57,682 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:57] "GET /?__debugger__=yes&cmd=resource&f=console.png&s=dFHiD1LLOU5uBZz3PCBH HTTP/1.1" 200 -
2025-07-09 19:55:57,703 INFO: 127.0.0.1 - - [09/Jul/2025 19:55:57] "GET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-07-09 19:56:25,288 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "GET / HTTP/1.1" 200 -
2025-07-09 19:56:25,323 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,331 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,336 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250617_202240_2.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,350 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,354 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250613_000452_Ou2ESueq.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,365 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250613_001329_HLk9Vn2d.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,395 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250611_221720_1.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,396 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250611_191431_2.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,406 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250612_151120_1.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,421 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250612_235643_q8gEGVAp.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,431 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250611_191905_dnspy.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:25,442 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:25] "[36mGET /uploads/icons/20250611_190659_venom.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:26,269 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:26] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 19:56:34,011 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "GET /app/14 HTTP/1.1" 200 -
2025-07-09 19:56:34,052 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 19:56:34,053 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:56:34,072 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "GET /uploads/screenshots/20250613_015832_Screenshot_1.webp HTTP/1.1" 200 -
2025-07-09 19:56:34,082 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:34,238 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:34] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-09 19:56:36,396 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:56:46,126 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "[32mGET /app/14 HTTP/1.1[0m" 302 -
2025-07-09 19:56:46,145 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "GET /gate?next=/app/14 HTTP/1.1" 200 -
2025-07-09 19:56:46,435 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:56:46,439 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 19:56:46,444 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-07-09 19:56:46,470 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:46] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-09 19:56:47,453 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:47] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-07-09 19:56:47,818 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:47] "POST /fp/collect HTTP/1.1" 200 -
2025-07-09 19:56:48,372 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "GET /app/14 HTTP/1.1" 200 -
2025-07-09 19:56:48,409 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-07-09 19:56:48,417 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "[36mGET /uploads/icons/20250613_015832_2.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:48,420 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-07-09 19:56:48,422 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "[36mGET /uploads/screenshots/20250613_015832_Screenshot_1.webp HTTP/1.1[0m" 304 -
2025-07-09 19:56:48,628 INFO: 127.0.0.1 - - [09/Jul/2025 19:56:48] "GET /fp/nonce HTTP/1.1" 200 -
2025-07-09 20:02:41,606 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\security.py', reloading
2025-07-09 20:02:41,709 INFO:  * Restarting with stat
2025-07-09 20:02:43,416 WARNING:  * Debugger is active!
2025-07-09 20:02:43,427 INFO:  * Debugger PIN: 437-432-840
2025-07-09 20:16:16,327 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 20:16:16,394 INFO:  * Restarting with stat
2025-07-09 20:16:17,512 WARNING:  * Debugger is active!
2025-07-09 20:16:17,540 INFO:  * Debugger PIN: 437-432-840
2025-07-09 20:16:29,741 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 20:16:29,824 INFO:  * Restarting with stat
2025-07-09 20:16:30,731 WARNING:  * Debugger is active!
2025-07-09 20:16:30,738 INFO:  * Debugger PIN: 437-432-840
2025-07-09 20:17:24,201 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 20:17:24,202 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 20:18:41,621 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:41] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:42,668 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:42] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:43,698 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:43] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:44,749 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:44] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:46,224 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:46] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:47,280 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:47] "GET / HTTP/1.1" 200 -
2025-07-09 20:18:48,305 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:48] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:49,327 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:49] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:18:50,340 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:50] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:51,359 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:51] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:18:52,423 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:52] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:53,438 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:53] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:18:54,469 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:54] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:55,489 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:55] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:18:56,503 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:56] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:57,522 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:57] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:18:58,538 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:58] "[32mGET /sitemap.xml HTTP/1.1[0m" 302 -
2025-07-09 20:18:59,555 INFO: 127.0.0.1 - - [09/Jul/2025 20:18:59] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:00,572 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:00] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:01,588 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:01] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:02,606 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:02] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:03,636 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:03] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:04,654 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:04] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:05,677 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:05] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:06,701 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:06] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:07,722 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:07] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:08,741 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:08] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:09,761 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:09] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:10,788 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:10] "[32mGET /robots.txt HTTP/1.1[0m" 302 -
2025-07-09 20:19:11,821 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:11] "GET /gate?next=/ HTTP/1.1" 200 -
2025-07-09 20:19:12,852 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:12] "GET / HTTP/1.1" 200 -
2025-07-09 20:19:13,901 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:13] "GET / HTTP/1.1" 200 -
2025-07-09 20:19:14,943 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:14] "GET / HTTP/1.1" 200 -
2025-07-09 20:19:15,985 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:15] "GET / HTTP/1.1" 200 -
2025-07-09 20:19:17,035 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:17] "GET / HTTP/1.1" 200 -
2025-07-09 20:19:18,063 INFO: 127.0.0.1 - - [09/Jul/2025 20:19:18] "GET / HTTP/1.1" 200 -
2025-07-09 20:59:49,308 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 20:59:49,308 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 21:00:01,403 INFO: 127.0.0.1 - - [09/Jul/2025 21:00:01] "[32mGET /app/7 HTTP/1.1[0m" 302 -
2025-07-09 21:00:14,816 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:00:14,880 INFO:  * Restarting with stat
2025-07-09 21:00:15,680 WARNING:  * Debugger is active!
2025-07-09 21:00:15,705 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:00:18,875 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:00:21,573 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 21:00:21,574 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 21:00:21,574 INFO:  * Restarting with stat
2025-07-09 21:00:22,300 WARNING:  * Debugger is active!
2025-07-09 21:00:22,356 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:01:00,304 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 21:01:00,305 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 21:01:00,306 INFO:  * Restarting with stat
2025-07-09 21:01:00,999 WARNING:  * Debugger is active!
2025-07-09 21:01:01,003 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:01:43,719 INFO: 127.0.0.1 - - [09/Jul/2025 21:01:43] "[32mGET /app/7 HTTP/1.1[0m" 302 -
2025-07-09 21:02:25,694 INFO: 127.0.0.1 - - [09/Jul/2025 21:02:25] "[32mGET /app/7 HTTP/1.1[0m" 302 -
2025-07-09 21:02:58,454 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:02:58,524 INFO:  * Restarting with stat
2025-07-09 21:02:59,185 WARNING:  * Debugger is active!
2025-07-09 21:02:59,189 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:03:04,271 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:03:04,342 INFO:  * Restarting with stat
2025-07-09 21:03:04,976 WARNING:  * Debugger is active!
2025-07-09 21:03:04,980 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:03:07,030 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:03:07,113 INFO:  * Restarting with stat
2025-07-09 21:03:07,872 WARNING:  * Debugger is active!
2025-07-09 21:03:07,876 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:03:30,146 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:03:30,223 INFO:  * Restarting with stat
2025-07-09 21:03:31,065 WARNING:  * Debugger is active!
2025-07-09 21:03:31,074 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:03:41,211 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:03:41,295 INFO:  * Restarting with stat
2025-07-09 21:03:42,039 WARNING:  * Debugger is active!
2025-07-09 21:03:42,043 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:08:02,967 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\main.py', reloading
2025-07-09 21:08:03,038 INFO:  * Restarting with stat
2025-07-09 21:08:03,671 WARNING:  * Debugger is active!
2025-07-09 21:08:03,678 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:08:15,847 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-07-09 21:08:15,994 INFO:  * Restarting with stat
2025-07-09 21:08:16,581 WARNING:  * Debugger is active!
2025-07-09 21:08:16,585 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:08:52,119 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-07-09 21:08:52,121 INFO: [33mPress CTRL+C to quit[0m
2025-07-09 21:09:40,959 INFO: 127.0.0.1 - - [09/Jul/2025 21:09:40] "GET / HTTP/1.1" 200 -
2025-07-09 21:09:41,993 INFO: 127.0.0.1 - - [09/Jul/2025 21:09:41] "GET / HTTP/1.1" 200 -
2025-07-09 21:09:43,027 INFO: 127.0.0.1 - - [09/Jul/2025 21:09:43] "GET / HTTP/1.1" 200 -
2025-07-09 21:09:44,057 INFO: 127.0.0.1 - - [09/Jul/2025 21:09:44] "GET /sitemap.xml HTTP/1.1" 200 -
2025-07-09 21:09:45,098 INFO: 127.0.0.1 - - [09/Jul/2025 21:09:45] "GET /sitemap.xml HTTP/1.1" 200 -
2025-07-09 21:13:47,387 INFO: 127.0.0.1 - - [09/Jul/2025 21:13:47] "GET /app/7 HTTP/1.1" 200 -
2025-07-09 21:13:50,166 INFO: 127.0.0.1 - - [09/Jul/2025 21:13:50] "GET /app/7 HTTP/1.1" 200 -
2025-07-09 21:13:55,687 INFO: 127.0.0.1 - - [09/Jul/2025 21:13:55] "[32mGET /app/7 HTTP/1.1[0m" 302 -
2025-07-09 21:15:09,086 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-07-09 21:15:09,160 INFO:  * Restarting with stat
2025-07-09 21:15:10,352 WARNING:  * Debugger is active!
2025-07-09 21:15:10,359 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:17:01,656 INFO: 127.0.0.1 - - [09/Jul/2025 21:17:01] "[32mGET /app/7 HTTP/1.1[0m" 302 -
2025-07-09 21:21:01,007 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:21:01,073 INFO:  * Restarting with stat
2025-07-09 21:21:01,982 WARNING:  * Debugger is active!
2025-07-09 21:21:01,985 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:21:31,328 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:21:31,389 INFO:  * Restarting with stat
2025-07-09 21:21:32,245 WARNING:  * Debugger is active!
2025-07-09 21:21:32,248 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:21:58,637 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:21:58,702 INFO:  * Restarting with stat
2025-07-09 21:21:59,568 WARNING:  * Debugger is active!
2025-07-09 21:21:59,578 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:22:58,997 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:22:59,103 INFO:  * Restarting with stat
2025-07-09 21:23:00,434 WARNING:  * Debugger is active!
2025-07-09 21:23:00,439 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:26:46,729 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:26:46,804 INFO:  * Restarting with stat
2025-07-09 21:26:47,423 WARNING:  * Debugger is active!
2025-07-09 21:26:47,427 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:26:50,491 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:26:50,552 INFO:  * Restarting with stat
2025-07-09 21:26:51,271 WARNING:  * Debugger is active!
2025-07-09 21:26:51,271 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:26:55,345 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:26:55,401 INFO:  * Restarting with stat
2025-07-09 21:26:56,015 WARNING:  * Debugger is active!
2025-07-09 21:26:56,041 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:26:58,134 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:26:58,241 INFO:  * Restarting with stat
2025-07-09 21:26:58,920 WARNING:  * Debugger is active!
2025-07-09 21:26:58,924 INFO:  * Debugger PIN: 437-432-840
2025-07-09 21:29:12,270 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\seo.py', reloading
2025-07-09 21:29:12,402 INFO:  * Restarting with stat
2025-07-09 21:29:13,975 WARNING:  * Debugger is active!
2025-07-09 21:29:13,980 INFO:  * Debugger PIN: 437-432-840
