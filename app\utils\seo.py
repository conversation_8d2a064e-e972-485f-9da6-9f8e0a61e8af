"""
SEO utilities for search engine optimization and bot detection
"""
import re
from datetime import datetime
from urllib.parse import urljoin
from flask import request, url_for, current_app
from app.models import App, get_db


class BotDetector:
    """Detect and handle search engine bots"""
    
    # Known search engine bot user agents (partial matches)
    SEARCH_ENGINE_BOTS = [
        'googlebot',
        'bingbot',
        'slurp',  # Yahoo
        'duckduckbot',
        'baiduspider',
        'yandexbot',
        'facebookexternalhit',
        'twitterbot',
        'linkedinbot',
        'whatsapp',
        'telegrambot',
        'discordbot',  # Discord link previews
        'discord',     # Discord alternative user agent
        'telegram',    # Telegram link previews
        'applebot',
        'ia_archiver',  # Internet Archive
        'archive.org_bot',
        'msnbot',
        'ahrefsbot',
        'semrushbot',
        'mj12bot',
        'dotbot',
        'petalbot',
        'skypeuripreview',  # Skype link previews
        'slackbot',    # Slack link previews
        'vkshare',     # V<PERSON> link previews
        'redditbot',   # Reddit link previews
        'pinterest',   # Pinterest link previews
        'tumblr',      # Tumblr link previews
        'embedly',     # Embedly service
        'outbrain',    # Outbrain content discovery
        'flipboard'    # Flipboard link previews
    ]
    
    # Known search engine IP ranges (simplified - in production use more comprehensive lists)
    SEARCH_ENGINE_IPS = [
        # Google
        '66.249.',
        '64.233.',
        '72.14.',
        '74.125.',
        '209.85.',
        '216.239.',
        # Bing
        '65.52.',
        '65.55.',
        '131.253.',
        '157.54.',
        '157.55.',
        '207.46.',
        # Yahoo
        '68.142.',
        '72.30.',
        '74.6.',
        '98.136.',
        '202.160.',
        # Baidu
        '123.125.',
        '220.181.',
        # Yandex
        '5.45.',
        '37.9.',
        '77.88.',
        '87.250.',
        '95.108.',
        '178.154.',
        '199.21.'
    ]
    
    @classmethod
    def is_search_engine_bot(cls, user_agent=None, ip_address=None):
        """
        Check if the request is from a search engine bot
        Returns: (is_bot, bot_name)
        """
        if not user_agent:
            user_agent = request.headers.get('User-Agent', '').lower()
        else:
            user_agent = user_agent.lower()
            
        if not ip_address:
            from app.utils.common import get_client_ip
            ip_address = get_client_ip()
        
        # Check user agent
        for bot in cls.SEARCH_ENGINE_BOTS:
            if bot in user_agent:
                return True, bot
        
        # Check IP ranges
        for ip_prefix in cls.SEARCH_ENGINE_IPS:
            if ip_address.startswith(ip_prefix):
                return True, f"ip_range_{ip_prefix}"
        
        return False, None
    
    @classmethod
    def should_bypass_security(cls, user_agent=None, ip_address=None):
        """
        Determine if security checks should be bypassed for this request
        """
        is_bot, bot_name = cls.is_search_engine_bot(user_agent, ip_address)
        return is_bot


class MetadataGenerator:
    """Generate dynamic metadata for pages"""
    
    DEFAULT_TITLE = "PEPE Store - Top #1 Tools for PC"
    DEFAULT_DESCRIPTION = "Discover powerful tools and premium software for your PC at PEPE Store. Fast downloads, top-rated apps, and more."
    DEFAULT_KEYWORDS = "PEPE Store, PC tools, software for PC, premium apps, download software, top PC applications, PC utilities"
    DEFAULT_IMAGE = "https://pepestore.pythonanywhere.com/static/assets/pepe.jpeg"
    SITE_URL = "https://pepestore.pythonanywhere.com"
    
    @classmethod
    def generate_app_metadata(cls, app):
        """Generate metadata for app detail pages"""
        if not app:
            return cls.get_default_metadata()
        
        title = f"{app['name']} - Download Free | PEPE Store"
        description = f"Download {app['name']} v{app['version']} for free. {app.get('description', '')[:150]}..."
        keywords = f"{app['name']}, {app.get('category', '')}, PC software, download, {cls.DEFAULT_KEYWORDS}"
        
        # Use app icon if available, otherwise default
        image = cls.DEFAULT_IMAGE
        if app.get('icon_path'):
            image = urljoin(cls.SITE_URL, url_for('main.uploaded_file', filename=app['icon_path']))
        
        return {
            'title': title,
            'description': description,
            'keywords': keywords,
            'image': image,
            'url': f"{cls.SITE_URL}/app/{app['id']}",
            'type': 'article'
        }
    
    @classmethod
    def generate_category_metadata(cls, category):
        """Generate metadata for category pages"""
        if not category:
            return cls.get_default_metadata()
        
        title = f"{category} Software - Free Downloads | PEPE Store"
        description = f"Browse and download the best {category} software for PC. Free, safe, and fast downloads from PEPE Store."
        keywords = f"{category}, {category} software, PC tools, {cls.DEFAULT_KEYWORDS}"
        
        return {
            'title': title,
            'description': description,
            'keywords': keywords,
            'image': cls.DEFAULT_IMAGE,
            'url': f"{cls.SITE_URL}/?category={category}",
            'type': 'website'
        }
    
    @classmethod
    def generate_search_metadata(cls, search_term):
        """Generate metadata for search results pages"""
        if not search_term:
            return cls.get_default_metadata()
        
        title = f"Search Results for '{search_term}' | PEPE Store"
        description = f"Find software and tools related to '{search_term}' on PEPE Store. Download free PC applications and utilities."
        keywords = f"{search_term}, search, PC software, {cls.DEFAULT_KEYWORDS}"
        
        return {
            'title': title,
            'description': description,
            'keywords': keywords,
            'image': cls.DEFAULT_IMAGE,
            'url': f"{cls.SITE_URL}/?search={search_term}",
            'type': 'website'
        }
    
    @classmethod
    def get_default_metadata(cls):
        """Get default metadata for pages"""
        return {
            'title': cls.DEFAULT_TITLE,
            'description': cls.DEFAULT_DESCRIPTION,
            'keywords': cls.DEFAULT_KEYWORDS,
            'image': cls.DEFAULT_IMAGE,
            'url': cls.SITE_URL,
            'type': 'website'
        }


class SitemapGenerator:
    """Generate XML sitemaps"""

    SITE_URL = "https://pepestore.pythonanywhere.com"

    @classmethod
    def generate_sitemap_xml(cls):
        """Generate complete XML sitemap"""
        urls = []
        base_url = cls.SITE_URL

        # Add homepage
        urls.append({
            'loc': base_url + '/',
            'lastmod': datetime.now().strftime('%Y-%m-%d'),
            'changefreq': 'daily',
            'priority': '1.0'
        })

        # Add category pages
        try:
            categories = App.get_categories()
            for category in categories:
                urls.append({
                    'loc': f"{base_url}/?category={category}",
                    'lastmod': datetime.now().strftime('%Y-%m-%d'),
                    'changefreq': 'weekly',
                    'priority': '0.8'
                })
        except Exception:
            pass

        # Add app detail pages
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT id, updated_at FROM apps ORDER BY id')
                apps = cursor.fetchall()

                for app_id, updated_at in apps:
                    # Parse updated_at or use current date
                    try:
                        if updated_at:
                            lastmod = datetime.strptime(updated_at, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
                        else:
                            lastmod = datetime.now().strftime('%Y-%m-%d')
                    except:
                        lastmod = datetime.now().strftime('%Y-%m-%d')

                    urls.append({
                        'loc': f"{base_url}/app/{app_id}",
                        'lastmod': lastmod,
                        'changefreq': 'weekly',
                        'priority': '0.7'
                    })
        except Exception:
            pass

        # Add suggestions page
        urls.append({
            'loc': base_url + '/suggestions',
            'lastmod': datetime.now().strftime('%Y-%m-%d'),
            'changefreq': 'monthly',
            'priority': '0.5'
        })

        # Generate XML
        xml_content = '<?xml version="1.0" encoding="UTF-8"?>\n'
        xml_content += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'

        for url in urls:
            xml_content += '  <url>\n'
            xml_content += f'    <loc>{url["loc"]}</loc>\n'
            xml_content += f'    <lastmod>{url["lastmod"]}</lastmod>\n'
            xml_content += f'    <changefreq>{url["changefreq"]}</changefreq>\n'
            xml_content += f'    <priority>{url["priority"]}</priority>\n'
            xml_content += '  </url>\n'

        xml_content += '</urlset>'

        return xml_content

    @classmethod
    def generate_robots_txt(cls):
        """Generate robots.txt content"""
        robots_content = """# Robots.txt for PEPE Store
# Allow search engines to crawl the site

User-agent: *
Allow: /
Allow: /app/
Allow: /category/
Allow: /search
Allow: /suggestions
Allow: /static/

# Disallow admin and private areas
Disallow: /admin/
Disallow: /publisher/
Disallow: /auth/
Disallow: /api/
Disallow: /fp/
Disallow: /gate
Disallow: /uploads/apps/
Disallow: /s/

# Allow specific search engine bots
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

User-agent: Slurp
Allow: /

User-agent: DuckDuckBot
Allow: /

User-agent: Baiduspider
Allow: /

User-agent: YandexBot
Allow: /

# Allow social media and messaging platform bots for link previews
User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /

User-agent: WhatsApp
Allow: /

User-agent: TelegramBot
Allow: /

User-agent: DiscordBot
Allow: /

User-agent: Slackbot
Allow: /

User-agent: SkypeUriPreview
Allow: /

# Sitemap location
Sitemap: {sitemap_url}

# Crawl delay for general bots (not search engines)
Crawl-delay: 1
""".format(sitemap_url=cls.SITE_URL + '/sitemap.xml')

        return robots_content
